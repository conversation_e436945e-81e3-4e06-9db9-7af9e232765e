import { getById, getUserPresenceRecords, getLastPresenceRecord } from '$lib/db';
import type { PageServerLoad } from './$types';
import type { School, SchoolUser, PresenceRecord } from '$lib/types';

export const load: PageServerLoad = async ({ params }) => {
  const user = await getById<SchoolUser>(params.user);
  const school = await getById<School>(params.school);
  
  // Get all presence records for this user in this school
  const records = await getUserPresenceRecords(params.user, params.school);
  
  // Get the last presence record to determine current status
  const lastRecord = await getLastPresenceRecord(params.user, params.school);
  
  const currentStatus = {
    isSignedIn: lastRecord ? lastRecord.d === 1 : false,
    lastRecord
  };

  return {
    user,
    school,
    records,
    currentStatus
  };
};

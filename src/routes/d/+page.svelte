<script lang="ts">
	import { onMount } from 'svelte';
	import { scale, fade } from 'svelte/transition';

	export let data;
	let animateCards = false;

	onMount(async () => {
		animateCards = true;
	});
</script>

<div class="container-dream">
	{#if animateCards}
		<header class="section-whisper text-center" in:fade={{ duration: 800 }}>
			<h1 class="text-cosmos mb-4" style="color: var(--deep-ocean);">
				Dashboard
			</h1>
			<p class="text-wave text-whisper">
				Your peaceful space for managing educational experiences
			</p>
		</header>
	{/if}

	<!-- Schools List -->
	{#if data.schools.length > 0}
		<section class="mb-12">
			<h2 class="text-sky mb-8 text-center">Your Schools</h2>
			<div class="grid gap-8 md:grid-cols-2">
				{#each data.schools as school, i}
					{#if animateCards}
						<a
							href="/sc/{school.id}"
							class="card-dream hover-lift transition-float group"
							in:scale={{ delay: 200 + i * 100, duration: 500 }}
						>
							<div class="flex items-center space-x-6">
								<div
									class="w-16 h-16 flex items-center justify-center text-3xl transition-breath"
									style="background-color: var(--sky-breath-soft); color: var(--sky-breath);"
								>
									🏫
								</div>
								<div class="flex-1">
									<h3 class="text-tide font-semibold mb-1" style="color: var(--deep-ocean);">
										{school.n}
									</h3>
									<p class="text-breath text-whisper">Click to manage and explore</p>
								</div>
							</div>
						</a>
					{/if}
				{/each}
			</div>
		</section>
	{/if}

	<!-- Actions -->
	<section class="section-whisper">
		<h2 class="text-sky mb-8 text-center">Get Started</h2>
		<div class="grid gap-8 md:grid-cols-2 max-w-4xl mx-auto">
			{#if animateCards}
				<!-- Create School Card -->
				<a
					href="/sc/a"
					class="card-dream hover-lift transition-float group text-center p-8"
					in:scale={{ delay: 400, duration: 500 }}
				>
					<div class="space-y-6">
						<div
							class="w-20 h-20 mx-auto flex items-center justify-center text-4xl floating-whisper"
							style="background-color: var(--lavender-mist-soft); color: var(--lavender-mist);"
						>
							🏫
						</div>
						<div>
							<h3 class="text-horizon font-semibold mb-3" style="color: var(--deep-ocean);">
								Create School
							</h3>
							<p class="text-flow text-whisper leading-relaxed">
								Set up a new educational space and begin managing academic records with grace and simplicity
							</p>
						</div>
					</div>
				</a>

				<!-- Request Access Card -->
				<a
					href="/sc/ra"
					class="card-dream hover-lift transition-float group text-center p-8"
					in:scale={{ delay: 500, duration: 500 }}
				>
					<div class="space-y-6">
						<div
							class="w-20 h-20 mx-auto flex items-center justify-center text-4xl floating-whisper"
							style="background-color: var(--rose-embrace-soft); color: var(--rose-embrace);"
						>
							🎓
						</div>
						<div>
							<h3 class="text-horizon font-semibold mb-3" style="color: var(--deep-ocean);">
								Request Access
							</h3>
							<p class="text-flow text-whisper leading-relaxed">
								Join an existing school community as a student, teacher, or administrator
							</p>
						</div>
					</div>
				</a>
			{/if}
		</div>
	</section>
</div>

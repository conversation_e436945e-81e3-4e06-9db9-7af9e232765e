@import "tailwindcss";

@import "tw-animate-css";

@font-face {
  font-family: 'Allegro';
  src: url('/Allegro.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@custom-variant dark (&:is(.dark *));

:root {
  /*
   * DREAMY DESIGN SYSTEM
   * Inspired by the softest clouds, the gentlest morning light,
   * and the most peaceful moments of human experience.
   * Every pixel crafted for mental tranquility and visual bliss.
   */

  /* Core Philosophy: Zero Border Radius - Pure Minimalism */
  --radius: 0;
  --radius-card: 0;
  --radius-button: 0;
  --radius-input: 0;

  /*
   * ETHEREAL COLOR PALETTE
   * Each color chosen to evoke calm, peace, and gentle focus
   * No gradients - only pure, soft, breathing colors
   */

  /* Deep Ocean - The foundation of all thought */
  --deep-ocean: #000072;
  --deep-ocean-soft: rgba(0, 0, 114, 0.05);
  --deep-ocean-whisper: rgba(0, 0, 114, 0.02);

  /* <PERSON> Embrace - Warm, nurturing, human */
  --rose-embrace: #A53E60;
  --rose-embrace-soft: rgba(165, 62, 96, 0.08);
  --rose-embrace-whisper: rgba(165, 62, 96, 0.03);

  /* Plum Serenity - Balanced, grounded, wise */
  --plum-serenity: #6E3E60;
  --plum-serenity-soft: rgba(110, 62, 96, 0.06);
  --plum-serenity-whisper: rgba(110, 62, 96, 0.02);

  /* Sky Breath - Infinite possibility, clarity */
  --sky-breath: #36C6E0;
  --sky-breath-soft: rgba(54, 198, 224, 0.08);
  --sky-breath-whisper: rgba(54, 198, 224, 0.03);

  /* Coral Dream - Energy, life, gentle passion */
  --coral-dream: #DD3E60;
  --coral-dream-soft: rgba(221, 62, 96, 0.08);
  --coral-dream-whisper: rgba(221, 62, 96, 0.03);

  /* Lavender Mist - Creativity, imagination, peace */
  --lavender-mist: #A08BE0;
  --lavender-mist-soft: rgba(160, 139, 224, 0.08);
  --lavender-mist-whisper: rgba(160, 139, 224, 0.03);

  /*
   * SEMANTIC COLOR MAPPING
   * Connecting our ethereal palette to interface meaning
   */

  /* Pure foundation - like fresh snow on a quiet morning */
  --background: #fefefe;
  --background-soft: #fcfcfc;
  --background-whisper: #fafafa;

  /* Text that breathes with the soul */
  --foreground: var(--deep-ocean);
  --foreground-soft: rgba(0, 0, 114, 0.8);
  --foreground-whisper: rgba(0, 0, 114, 0.5);

  /* Cards that float like gentle thoughts */
  --card: #ffffff;
  --card-soft: rgba(255, 255, 255, 0.8);
  --card-foreground: var(--deep-ocean);

  /* Primary actions - the sky's gentle embrace */
  --primary: var(--sky-breath);
  --primary-foreground: #ffffff;
  --primary-hover: rgba(54, 198, 224, 0.9);
  --primary-soft: var(--sky-breath-soft);

  /* Secondary actions - plum's quiet wisdom */
  --secondary: var(--plum-serenity-soft);
  --secondary-foreground: var(--plum-serenity);
  --secondary-hover: rgba(110, 62, 96, 0.12);

  /* Muted elements - whispers of lavender */
  --muted: var(--lavender-mist-whisper);
  --muted-foreground: rgba(0, 0, 114, 0.6);
  --muted-hover: var(--lavender-mist-soft);

  /* Accent touches - rose's gentle warmth */
  --accent: var(--rose-embrace-soft);
  --accent-foreground: var(--rose-embrace);
  --accent-hover: rgba(165, 62, 96, 0.12);

  /* Gentle warnings - coral's caring attention */
  --destructive: var(--coral-dream);
  --destructive-foreground: #ffffff;
  --destructive-soft: var(--coral-dream-soft);

  /* Borders that barely exist - like morning mist */
  --border: rgba(0, 0, 114, 0.08);
  --border-soft: rgba(0, 0, 114, 0.04);
  --border-whisper: rgba(0, 0, 114, 0.02);

  /* Input fields - receptive and welcoming */
  --input: var(--background-whisper);
  --input-border: var(--border);
  --input-focus: var(--sky-breath);

  /* Focus rings - gentle attention, never harsh */
  --ring: rgba(54, 198, 224, 0.3);
  --ring-soft: rgba(54, 198, 224, 0.15);

  /*
   * DREAMY SHADOWS
   * Shadows that feel like gentle mist, never harsh or imposing
   * Each shadow tells a story of depth without weight
   */
  --shadow-whisper: 0 1px 3px 0 rgba(0, 0, 114, 0.03);
  --shadow-breath: 0 2px 8px 0 rgba(0, 0, 114, 0.04);
  --shadow-float: 0 4px 16px 0 rgba(0, 0, 114, 0.06);
  --shadow-dream: 0 8px 32px 0 rgba(0, 0, 114, 0.08);
  --shadow-transcend: 0 16px 64px 0 rgba(0, 0, 114, 0.1);

  /*
   * FLUID SPACING SYSTEM
   * Spacing that breathes, expands, and contracts like living tissue
   * Each measurement chosen for visual rhythm and mental peace
   */
  --space-atom: 0.125rem;    /* 2px - The smallest breath */
  --space-whisper: 0.25rem;  /* 4px - A gentle touch */
  --space-breath: 0.5rem;    /* 8px - Natural rhythm */
  --space-pulse: 0.75rem;    /* 12px - Heartbeat */
  --space-flow: 1rem;        /* 16px - Natural flow */
  --space-wave: 1.5rem;      /* 24px - Gentle wave */
  --space-tide: 2rem;        /* 32px - Ocean tide */
  --space-horizon: 3rem;     /* 48px - Distant horizon */
  --space-sky: 4rem;         /* 64px - Open sky */
  --space-cosmos: 6rem;      /* 96px - Infinite space */

  /*
   * TYPOGRAPHY SCALE
   * Text sizes that feel natural, never shouting, always whispering truth
   */
  --text-whisper: 0.75rem;   /* 12px */
  --text-breath: 0.875rem;   /* 14px */
  --text-flow: 1rem;         /* 16px */
  --text-wave: 1.125rem;     /* 18px */
  --text-tide: 1.25rem;      /* 20px */
  --text-horizon: 1.5rem;    /* 24px */
  --text-sky: 2rem;          /* 32px */
  --text-cosmos: 3rem;       /* 48px */

  /*
   * ANIMATION TIMING
   * Movements that feel like natural breathing, never rushed
   */
  --timing-instant: 0ms;
  --timing-breath: 150ms;
  --timing-heartbeat: 300ms;
  --timing-wave: 500ms;
  --timing-tide: 800ms;
  --timing-dream: 1200ms;

  /*
   * EASING FUNCTIONS
   * Curves that mirror the gentle acceleration of falling leaves
   */
  --ease-breath: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-float: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-dream: cubic-bezier(0.23, 1, 0.32, 1);
}

.dark {
  /*
   * DARK MODE - THE GENTLE NIGHT
   * Colors that embrace like twilight, never harsh or jarring
   * A sanctuary for tired eyes and peaceful minds
   */

  /* Night sky foundation */
  --background: #0a0a0f;
  --background-soft: #0f0f14;
  --background-whisper: #141419;

  /* Moonlight text */
  --foreground: rgba(255, 255, 255, 0.9);
  --foreground-soft: rgba(255, 255, 255, 0.7);
  --foreground-whisper: rgba(255, 255, 255, 0.5);

  /* Floating cards in the night */
  --card: rgba(255, 255, 255, 0.03);
  --card-soft: rgba(255, 255, 255, 0.02);
  --card-foreground: var(--foreground);

  /* Primary actions glow softly */
  --primary: var(--sky-breath);
  --primary-foreground: var(--deep-ocean);
  --primary-hover: rgba(54, 198, 224, 0.8);
  --primary-soft: rgba(54, 198, 224, 0.1);

  /* Secondary whispers in the dark */
  --secondary: rgba(110, 62, 96, 0.1);
  --secondary-foreground: var(--lavender-mist);
  --secondary-hover: rgba(110, 62, 96, 0.15);

  /* Muted elements fade into night */
  --muted: rgba(255, 255, 255, 0.05);
  --muted-foreground: rgba(255, 255, 255, 0.4);
  --muted-hover: rgba(255, 255, 255, 0.08);

  /* Accent glows warmly */
  --accent: rgba(165, 62, 96, 0.1);
  --accent-foreground: var(--rose-embrace);
  --accent-hover: rgba(165, 62, 96, 0.15);

  /* Gentle warnings in the night */
  --destructive: var(--coral-dream);
  --destructive-foreground: #ffffff;
  --destructive-soft: rgba(221, 62, 96, 0.1);

  /* Borders barely visible, like starlight */
  --border: rgba(255, 255, 255, 0.08);
  --border-soft: rgba(255, 255, 255, 0.04);
  --border-whisper: rgba(255, 255, 255, 0.02);

  /* Input fields welcome in the dark */
  --input: rgba(255, 255, 255, 0.03);
  --input-border: var(--border);
  --input-focus: var(--sky-breath);

  /* Focus rings glow gently */
  --ring: rgba(54, 198, 224, 0.4);
  --ring-soft: rgba(54, 198, 224, 0.2);

  /* Night shadows are deeper but still gentle */
  --shadow-whisper: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
  --shadow-breath: 0 2px 8px 0 rgba(0, 0, 0, 0.4);
  --shadow-float: 0 4px 16px 0 rgba(0, 0, 0, 0.5);
  --shadow-dream: 0 8px 32px 0 rgba(0, 0, 0, 0.6);
  --shadow-transcend: 0 16px 64px 0 rgba(0, 0, 0, 0.7);
}

@theme inline {
  /*
   * TAILWIND INTEGRATION
   * Mapping our dreamy design system to Tailwind's utility classes
   * Every class becomes a brushstroke of tranquility
   */

  /* Pure minimalism - no curves, only truth */
  --radius-sm: var(--radius);
  --radius-md: var(--radius);
  --radius-lg: var(--radius);
  --radius-xl: var(--radius);

  /* Color harmony */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
}

@layer base {
  /*
   * FOUNDATIONAL RESET
   * Every element begins in a state of pure potential
   * Clean, minimal, ready to serve human needs
   */
  * {
    border: none;
    outline: none;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  *:focus-visible {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
  }

  /*
   * BODY - THE CANVAS OF DREAMS
   * The foundation upon which all beauty rests
   */
  body {
    background-color: var(--background);
    color: var(--foreground);
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: -0.01em;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color var(--timing-wave) var(--ease-breath),
                color var(--timing-wave) var(--ease-breath);
  }

  /*
   * TYPOGRAPHY HIERARCHY
   * Text that speaks in whispers, never shouts
   */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    letter-spacing: -0.02em;
    color: var(--foreground);
  }

  h1 { font-size: var(--text-cosmos); }
  h2 { font-size: var(--text-sky); }
  h3 { font-size: var(--text-horizon); }
  h4 { font-size: var(--text-tide); }
  h5 { font-size: var(--text-wave); }
  h6 { font-size: var(--text-flow); }

  p {
    font-size: var(--text-flow);
    line-height: 1.7;
    color: var(--foreground-soft);
  }

  small {
    font-size: var(--text-breath);
    color: var(--muted-foreground);
  }

  /*
   * INTERACTIVE ELEMENTS
   * Buttons and links that respond like living things
   */
  button, a {
    cursor: pointer;
    transition: all var(--timing-breath) var(--ease-breath);
  }

  button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  /*
   * FORM ELEMENTS
   * Inputs that welcome and embrace user intention
   */
  input, textarea, select {
    font-family: inherit;
    font-size: var(--text-flow);
    transition: all var(--timing-breath) var(--ease-breath);
  }

  /*
   * SELECTION STYLING
   * Even text selection should feel dreamy
   */
  ::selection {
    background-color: var(--primary-soft);
    color: var(--primary);
  }

  /*
   * SCROLLBAR STYLING
   * Scrollbars that barely exist, like gentle guides
   */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 0;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--border-soft);
  }
}

@layer components {
  /*
   * DREAMY COMPONENT LIBRARY
   * Each component crafted with the care of a master artisan
   * Every interaction designed to bring peace to the human soul
   */

  /*
   * CARDS - FLOATING THOUGHTS
   * Containers that hold content like gentle hands
   */
  .card-dream {
    background-color: var(--card);
    color: var(--card-foreground);
    border: 1px solid var(--border);
    box-shadow: var(--shadow-float);
    padding: var(--space-tide);
    transition: all var(--timing-wave) var(--ease-float);
    backdrop-filter: blur(20px);
  }

  .card-dream:hover {
    box-shadow: var(--shadow-dream);
    transform: translateY(-2px);
  }

  .card-whisper {
    background-color: var(--card-soft);
    color: var(--card-foreground);
    border: 1px solid var(--border-soft);
    box-shadow: var(--shadow-whisper);
    padding: var(--space-wave);
    transition: all var(--timing-breath) var(--ease-breath);
  }

  .card-float {
    background-color: var(--card);
    color: var(--card-foreground);
    border: 1px solid var(--border);
    box-shadow: var(--shadow-transcend);
    padding: var(--space-horizon);
    transition: all var(--timing-dream) var(--ease-dream);
    backdrop-filter: blur(40px);
  }

  /*
   * BUTTONS - GENTLE INVITATIONS
   * Interactive elements that respond with grace
   */
  .btn-dream {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-pulse) var(--space-tide);
    font-size: var(--text-flow);
    font-weight: 500;
    background-color: var(--primary);
    color: var(--primary-foreground);
    border: none;
    cursor: pointer;
    transition: all var(--timing-breath) var(--ease-breath);
    position: relative;
    overflow: hidden;
  }

  .btn-dream:hover {
    background-color: var(--primary-hover);
    box-shadow: var(--shadow-float);
    transform: translateY(-1px);
  }

  .btn-dream:active {
    transform: translateY(0);
    box-shadow: var(--shadow-breath);
  }

  .btn-dream:focus-visible {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
  }

  .btn-whisper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-pulse) var(--space-tide);
    font-size: var(--text-flow);
    font-weight: 500;
    background-color: var(--secondary);
    color: var(--secondary-foreground);
    border: 1px solid var(--border);
    cursor: pointer;
    transition: all var(--timing-breath) var(--ease-breath);
  }

  .btn-whisper:hover {
    background-color: var(--secondary-hover);
    border-color: var(--border-soft);
    box-shadow: var(--shadow-breath);
  }

  .btn-ghost {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-pulse) var(--space-tide);
    font-size: var(--text-flow);
    font-weight: 500;
    background-color: transparent;
    color: var(--foreground);
    border: none;
    cursor: pointer;
    transition: all var(--timing-breath) var(--ease-breath);
  }

  .btn-ghost:hover {
    background-color: var(--muted);
    color: var(--foreground);
  }

  .btn-accent {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-pulse) var(--space-tide);
    font-size: var(--text-flow);
    font-weight: 500;
    background-color: var(--accent);
    color: var(--accent-foreground);
    border: 1px solid var(--border);
    cursor: pointer;
    transition: all var(--timing-breath) var(--ease-breath);
  }

  .btn-accent:hover {
    background-color: var(--accent-hover);
    box-shadow: var(--shadow-breath);
  }

  /*
   * INPUTS - RECEPTIVE VESSELS
   * Form elements that welcome user thoughts
   */
  .input-dream {
    width: 100%;
    padding: var(--space-pulse) var(--space-wave);
    font-size: var(--text-flow);
    background-color: var(--input);
    color: var(--foreground);
    border: 1px solid var(--input-border);
    transition: all var(--timing-breath) var(--ease-breath);
  }

  .input-dream::placeholder {
    color: var(--muted-foreground);
  }

  .input-dream:focus {
    outline: none;
    border-color: var(--input-focus);
    box-shadow: 0 0 0 3px var(--ring-soft);
    background-color: var(--background);
  }

  .input-whisper {
    width: 100%;
    padding: var(--space-breath) var(--space-pulse);
    font-size: var(--text-breath);
    background-color: var(--muted);
    color: var(--foreground);
    border: 1px solid var(--border-whisper);
    transition: all var(--timing-breath) var(--ease-breath);
  }

  .input-whisper:focus {
    outline: none;
    border-color: var(--primary);
    background-color: var(--background);
  }

  /*
   * LAYOUT CONTAINERS
   * Spaces that organize content with breathing room
   */
  .container-dream {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-tide);
  }

  .container-whisper {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--space-wave);
  }

  .section-dream {
    padding: var(--space-cosmos) 0;
  }

  .section-whisper {
    padding: var(--space-horizon) 0;
  }

  /*
   * TEXT UTILITIES
   * Typography helpers for perfect harmony
   */
  .text-dream {
    color: var(--foreground);
    line-height: 1.7;
  }

  .text-whisper {
    color: var(--muted-foreground);
    line-height: 1.6;
  }

  .text-accent {
    color: var(--accent-foreground);
  }

  .text-primary {
    color: var(--primary);
  }

  /*
   * SPACING UTILITIES
   * Consistent spacing that creates visual rhythm
   */
  .space-dream > * + * {
    margin-top: var(--space-tide);
  }

  .space-whisper > * + * {
    margin-top: var(--space-wave);
  }

  .space-breath > * + * {
    margin-top: var(--space-breath);
  }

  /*
   * FLOATING ELEMENTS
   * Background elements that add depth without distraction
   */
  .floating-dream {
    position: absolute;
    pointer-events: none;
    opacity: 0.1;
    animation: float-dream var(--timing-dream) var(--ease-dream) infinite;
  }

  .floating-whisper {
    position: absolute;
    pointer-events: none;
    opacity: 0.05;
    animation: float-whisper calc(var(--timing-dream) * 1.5) var(--ease-float) infinite;
  }
}

@layer utilities {
  /*
   * SHADOW UTILITIES
   * Depth that feels natural, never artificial
   */
  .shadow-whisper {
    box-shadow: var(--shadow-whisper);
  }

  .shadow-breath {
    box-shadow: var(--shadow-breath);
  }

  .shadow-float {
    box-shadow: var(--shadow-float);
  }

  .shadow-dream {
    box-shadow: var(--shadow-dream);
  }

  .shadow-transcend {
    box-shadow: var(--shadow-transcend);
  }

  /*
   * TRANSITION UTILITIES
   * Smooth movements that feel alive
   */
  .transition-dream {
    transition: all var(--timing-dream) var(--ease-dream);
  }

  .transition-float {
    transition: all var(--timing-wave) var(--ease-float);
  }

  .transition-breath {
    transition: all var(--timing-breath) var(--ease-breath);
  }

  /*
   * BACKDROP UTILITIES
   * Subtle blurs that add depth
   */
  .backdrop-dream {
    backdrop-filter: blur(20px);
  }

  .backdrop-whisper {
    backdrop-filter: blur(10px);
  }

  /*
   * INTERACTION UTILITIES
   * Hover and focus states that respond naturally
   */
  .hover-lift:hover {
    transform: translateY(-2px);
  }

  .hover-glow:hover {
    box-shadow: var(--shadow-dream);
  }

  .hover-fade:hover {
    opacity: 0.8;
  }
}

/*
 * DREAMY ANIMATIONS
 * Movements inspired by nature - clouds, waves, breathing
 */
@keyframes float-dream {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-8px) translateX(4px) rotate(0.5deg);
  }
  50% {
    transform: translateY(-12px) translateX(0px) rotate(0deg);
  }
  75% {
    transform: translateY(-8px) translateX(-4px) rotate(-0.5deg);
  }
}

@keyframes float-whisper {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
  }
  33% {
    transform: translateY(-6px) translateX(3px) scale(1.02);
  }
  66% {
    transform: translateY(-4px) translateX(-3px) scale(0.98);
  }
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-scale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/*
 * LEGACY COMPATIBILITY
 * Maintaining compatibility with existing components
 */
.card-soft {
  @apply card-dream;
}

.btn-soft {
  @apply btn-dream;
}

.btn-soft-secondary {
  @apply btn-whisper;
}

.btn-soft-ghost {
  @apply btn-ghost;
}

.input-soft {
  @apply input-dream;
}

.container-soft {
  @apply container-dream;
}

.section-soft {
  @apply section-dream;
}

.shadow-soft {
  @apply shadow-float;
}

.shadow-soft-lg {
  @apply shadow-dream;
}

.shadow-soft-xl {
  @apply shadow-transcend;
}